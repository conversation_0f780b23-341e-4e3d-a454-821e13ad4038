# pearl-frontend

The [Pearl](https://heypearl.ai) backend monorepo, which includes the Pearl web app, and the Pearl mobile app.

## Getting Started

### Pre-requisites

#### Node.js

It is recommended to use [nvm](https://github.com/nvm-sh/nvm)

The exact node version is specified in the `.nvmrc` file. To install the correct version, run:

```bash
nvm install
```

#### PNPM

[PNPM](https://pnpm.io/) is the package manager used in this project. To install it, run:

```bash
brew install pnpm
```

#### Pre-commit

- Install [pre-commit](https://pre-commit.com/) globally:

```bash
pip install pre-commit
```

- Install the pre-commit hooks:

```bash
pre-commit install --hook-type pre-commit --hook-type pre-push
```

#### Working with an IDE

Open the project from the **root directory** in your IDE.

### Install dependencies

```bash
pnpm --filter '*' install
```

Note: The `--filter '*'` flag is required as recursive install is disabled (see [`pnpm-workspace.yaml`](https://github.com/heypearl-ai/pearl-frontend/blob/main/pnpm-workspace.yaml)).

### Run the development web server

```bash
pnpm dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

### Add a new dependency

To add a new dependency to the **whole project**, use the following command from the root directory:

```bash
pnpm add <dependency-name>
```

To add a new dependency to a **specific package**, use the following command from the root directory:

```bash
pnpm --filter <package-name> add <dependency-name>
```

e.g.

```bash
pnpm --filter web add <dependency-name>
```

To add a dev dependency, use the `--save-dev` flag:

```bash
pnpm add --save-dev <dependency-name>
```
