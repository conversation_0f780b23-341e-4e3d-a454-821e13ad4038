# Agent Guidelines for Pearl Frontend

## Build/Test Commands
- `pnpm dev` - Start development server
- `pnpm run lint` - Run ESLint (mobile: `cd apps/mobile && pnpm lint`)
- `pnpm run prettier:check` - Check formatting (mobile: `cd apps/mobile && pnpm prettier:check`)
- `pnpm run tsc:check` - TypeScript check (mobile: `cd apps/mobile && pnpm tsc:check`)
- `pnpm test` - Run tests (mobile: `cd apps/mobile && pnpm test`)
- `pnpm test --testNamePattern="specific test"` - Run single test

## Code Style - General
- Use TypeScript with strict typing and interfaces
- Import order: builtin → external → type → internal → parent → sibling → index (alphabetical within groups)
- No relative imports - use absolute paths with @ aliases
- Functional components with TypeScript interfaces
- Minimize useEffect, useMemo, useCallback usage
- Use Jotai for global state management, local state when possible

## Mobile App (apps/mobile) - Tamagui + React Native
- Use Tamagui components (Stack, XStack, YStack) for layout
- Import Text from @components/primitives/Text
- Use `const { theme } = useTheme();` for theming
- Leverage <PERSON>'s style props instead of inline styles
- Create reusable primitives in @components/primitives/
- Follow existing component structure patterns from .cursor/rules/

## Web App (apps/web) - shadcn/ui + Tailwind CSS
- Use shadcn/ui components from @/components/ui/
- Use Tailwind CSS classes for styling
- Follow shadcn/ui component patterns and variants
- Use CSS modules or Tailwind for custom styling