{"compilerOptions": {"target": "ES2017", "allowJs": true, "skipLibCheck": true, "strict": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "bundler", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "baseUrl": ".", "paths": {"@/mobile/*": ["apps/mobile/*"], "@/web/*": ["apps/web/src/*"], "@/api-client": ["packages/api-client"], "@/api-client/*": ["packages/api-client/src/*"], "@/api-hooks": ["packages/api-hooks"], "@/api-hooks/*": ["packages/api-hooks/src/*"], "@/locales": ["packages/locales"], "@/locales/*": ["packages/locales/*"]}}, "exclude": ["node_modules"]}