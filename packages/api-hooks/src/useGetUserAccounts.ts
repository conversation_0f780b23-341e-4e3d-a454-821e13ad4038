import { useQuery } from "@tanstack/react-query";

import { useApiClient } from "@/api-client";

export type Account = {
  crmId: string;
  crmName: string;
};

export const useGetUserAccounts = () => {
  const { apiClient } = useApiClient();
  return useQuery({
    queryKey: ["userAccounts"],
    queryFn: async () => {
      return await apiClient.get<Account[]>("/workspace/accounts");
    },
  });
};
