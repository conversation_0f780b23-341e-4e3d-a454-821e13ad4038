import { useMutation, useQueryClient } from "@tanstack/react-query";

import { useApiClient } from "@/api-client";

export function useSyncUserAccounts() {
  const { apiClient } = useApiClient();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async () => {
      return await apiClient.get("/workspace/sync_accounts");
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["userAccounts"] });
    },
  });
}
