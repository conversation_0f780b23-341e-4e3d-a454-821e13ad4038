import { useQuery } from "@tanstack/react-query";

import { useApiClient } from "@/api-client";

export type MemberProfile = {
  id: string;
  userId: string;
  organizationId: string;
  isAdmin: boolean;
  firstName: string;
  lastName: string;
  email: string;
};

export const useGetMemberProfileMe = () => {
  const { apiClient } = useApiClient();

  return useQuery({
    queryKey: ["memberProfile"],
    queryFn: async () =>
      await apiClient.get<MemberProfile>(`/workspace/member_profiles/me`),
  });
};
