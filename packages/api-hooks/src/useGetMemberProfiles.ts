import { useQuery } from "@tanstack/react-query";

import { useApiClient } from "@/api-client";
import { MemberProfile } from "@/api-hooks/useGetMemberProfileMe";

type MemberProfiles = {
  members: MemberProfile[];
};

export const useGetMemberProfiles = () => {
  const { apiClient } = useApiClient();

  return useQuery({
    queryKey: ["memberProfiles"],
    queryFn: async () => {
      return await apiClient.get<MemberProfiles>(
        `/workspace/admin/member_profiles`,
      );
    },
  });
};
