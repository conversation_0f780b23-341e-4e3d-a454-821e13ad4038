import { useQuery } from "@tanstack/react-query";

import { useApiClient } from "@/api-client";

export type UserCRM = {
  crm_name: string;
  crm_user_id: string;
};

export function useGetUserCRM() {
  const { apiClient } = useApiClient();
  return useQuery({
    queryKey: ["userCRM"],
    queryFn: async () => {
      return await apiClient.get<UserCRM | null>("/workspace/user_crm");
    },
  });
}
