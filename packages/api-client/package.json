{"name": "@pearl-frontend/api-client", "version": "0.1.0", "description": "", "engines": {"node": "22.14.0"}, "packageManager": "pnpm@10.10.0", "main": "index.ts", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "lint-staged": {"*.{ts,tsx,js,jsx}": ["eslint --fix", "prettier --write"], "*.{json,css,scss,md}": ["prettier --write"]}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"react": "^19.0.0"}, "devDependencies": {"@tanstack/eslint-plugin-query": "^5.74.7", "@types/react": "^19", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0"}}