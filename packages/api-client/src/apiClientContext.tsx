import { ReactNode, createContext, useContext } from "react";

import { ApiClient } from "@/api-client/apiClient";
import { TokenStorageAdapter } from "@/api-client/tokenStorage";

interface ApiClientContextValue {
  apiClient: ApiClient;
  tokenStorage: TokenStorageAdapter;
}

const ApiClientContext = createContext<ApiClientContextValue | undefined>(
  undefined,
);

export function ApiClientProvider({
  apiClient,
  tokenStorage,
  children,
}: {
  apiClient: ApiClient;
  tokenStorage: TokenStorageAdapter;
  children: ReactNode;
}) {
  return (
    <ApiClientContext.Provider value={{ apiClient, tokenStorage }}>
      {children}
    </ApiClientContext.Provider>
  );
}

export const useApiClient = (): ApiClientContextValue => {
  const context = useContext(ApiClientContext);
  if (!context) {
    throw new Error("useApiClient must be used within an ApiClientProvider");
  }
  return { apiClient: context.apiClient, tokenStorage: context.tokenStorage };
};
