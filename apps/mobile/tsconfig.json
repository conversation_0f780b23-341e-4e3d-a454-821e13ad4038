{
  "extends": "expo/tsconfig.base",
  "compilerOptions": {
    "jsx": "react-jsx",
    "strict": true,
    "baseUrl": "../../",
    "paths": {
      // Base
      "@/api-client": ["packages/api-client"],
      "@/api-client/*": ["packages/api-client/src/*"],
      "@/api-hooks": ["packages/api-hooks"],
      "@/api-hooks/*": ["packages/api-hooks/src/*"],
      "@/locales": ["packages/locales"],
      "@/locales/*": ["packages/locales/*"],
      // Mobile
      "@/*": ["apps/mobile/*"],
      "@app/*": ["apps/mobile/app/*"],
      "@assets/*": ["apps/mobile/assets/*"],
      "@components/*": ["apps/mobile/components/*"],
      "@constants/*": ["apps/mobile/constants/*"],
      "@hooks/*": ["apps/mobile/hooks/*"],
      "@services/*": ["apps/mobile/services/*"],
      "@store/*": ["apps/mobile/store/*"],
      "@theme/*": ["apps/mobile/theme/*"],
      "@utils/*": ["apps/mobile/utils/*"],
      "@state/*": ["apps/mobile/state/*"]
    },
    "incremental": true,
    "tsBuildInfoFile": "./.cache/tsconfig.tsbuildinfo",
    "skipLibCheck": true,
    "useUnknownInCatchVariables": false
  },
  "include": ["**/*.ts", "**/*.tsx", ".expo/types/**/*.ts", "expo-env.d.ts"]
}
