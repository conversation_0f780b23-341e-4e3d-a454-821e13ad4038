import "@utils/i18n";
import "@/polyfills";

import { ApiClientProvider, AuthProvider } from "@/api-client";
import { queryClient, QueryClientProvider } from "@/api-hooks";
import { useReactQueryDevTools } from "@dev-plugins/react-query";
import {
  Inter_400Regular,
  Inter_500Medium,
  Inter_600SemiBold,
  Inter_700Bold,
} from "@expo-google-fonts/inter";
import { ThemeProvider } from "@react-navigation/native";
import { ToastProvider, ToastViewport } from "@tamagui/toast";
import { registerDevMenuItems } from "expo-dev-menu";
import { useFonts } from "expo-font";
import { SplashScreen, Stack } from "expo-router";
import { StatusBar } from "expo-status-bar";
import { useEffect } from "react";
import { GestureHandlerRootView } from "react-native-gesture-handler";
import { KeyboardProvider } from "react-native-keyboard-controller";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { TamaguiProvider } from "tamagui";

import { EnvIndicator } from "@/components/layout/EnvIndicator";
import { IS_DEVELOPMENT } from "@/constants/environment";
import { devMenuItems } from "@/utils/devMenu";
import { ToggleThemeButton } from "@components/ToggleThemeButton";
import { Header } from "@components/navigation/header/Header";
import { CurrentToast } from "@components/toast/CurrentToast";
import { useTheme } from "@hooks/useTheme";
import { apiClient } from "@services/api/apiClient";
import { tokenStorage } from "@services/api/tokenStorage";
import {
  darkNavigationTheme,
  lightNavigationTheme,
} from "@theme/navigationTheme";

import config from "../tamagui.config";

if (IS_DEVELOPMENT) {
  registerDevMenuItems(devMenuItems);
}

export {
  // Catch any errors thrown by the Layout component.
  ErrorBoundary,
} from "expo-router";

export const unstable_settings = {
  // Ensure that reloading on `/modal` keeps a back button present.
  initialRouteName: "index",
};

// Prevent the splash screen from auto-hiding before asset loading is complete.
SplashScreen.preventAutoHideAsync();

export default function RootLayout() {
  const { theme, isDarkTheme } = useTheme();
  const insets = useSafeAreaInsets();

  useReactQueryDevTools(queryClient);

  const [interLoaded, interError] = useFonts({
    InterRegular: Inter_400Regular,
    InterMedium: Inter_500Medium,
    InterSemiBold: Inter_600SemiBold,
    InterBold: Inter_700Bold,
  });

  useEffect(() => {
    if (interLoaded || interError) {
      // Hide the splash screen after the fonts have loaded (or an error was returned) and the UI is ready.
      SplashScreen.hideAsync();
    }
  }, [interLoaded, interError]);

  if (!interLoaded && !interError) {
    return null;
  }

  return (
    <ApiClientProvider apiClient={apiClient} tokenStorage={tokenStorage}>
      <AuthProvider>
        <QueryClientProvider>
          <ThemeProvider
            value={isDarkTheme ? darkNavigationTheme : lightNavigationTheme}
          >
            <TamaguiProvider config={config} defaultTheme={theme}>
              <KeyboardProvider>
                <GestureHandlerRootView style={{ flex: 1 }}>
                  <ToastProvider
                    swipeDirection="horizontal"
                    duration={1000}
                    native="mobile"
                    burntOptions={{ from: "bottom" }}
                  >
                    <StatusBar style={isDarkTheme ? "light" : "dark"} />
                    <RootLayoutNav />
                    <EnvIndicator />
                    <CurrentToast />
                    <ToastViewport
                      top={insets.top}
                      left={insets.left}
                      right={insets.right}
                      bottom={insets.bottom}
                    />
                  </ToastProvider>
                </GestureHandlerRootView>
              </KeyboardProvider>
            </TamaguiProvider>
          </ThemeProvider>
        </QueryClientProvider>
      </AuthProvider>
    </ApiClientProvider>
  );
}

const RootLayoutNav = () => {
  return (
    <Stack
      screenOptions={{
        headerTransparent: true,
        header: (props) => <Header {...props} />,
        title: "",
        headerRight: () => <ToggleThemeButton />,
      }}
    >
      <Stack.Screen name="login" />
      <Stack.Screen name="verifyOTC" />
      <Stack.Screen name="(app)" options={{ headerShown: false }} />
      <Stack.Screen name="onboarding" />
      <Stack.Screen name="salesforce" />
      <Stack.Screen name="salesforce-callback" />
      <Stack.Screen name="salesforce-login" />
      <Stack.Screen name="error" />
    </Stack>
  );
};
