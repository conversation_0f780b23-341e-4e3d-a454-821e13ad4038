import { useGetUserCRM, useSalesforceCallback } from "@/api-hooks";
import { useLocalSearchParams, useRouter } from "expo-router";
import { useEffect } from "react";

import { Layout } from "@/components/layout/Layout";
import {
  SalesforceErrorStep,
  SalesforceLoadingStep,
  SalesforceSuccessStep,
} from "@/components/salesforce";
import { usePreventBack } from "@/hooks/usePreventBack";

export default function SalesforceCallbackScreen() {
  const router = useRouter();

  const { refetch: refreshUserCRM } = useGetUserCRM();

  const { code, state } = useLocalSearchParams<{
    code: string;
    state: string;
  }>();

  usePreventBack();

  const {
    isLoading,
    isImporting,
    isSuccess,
    isError,
    errorMessage,
    processCallback,
  } = useSalesforceCallback();

  useEffect(() => {
    if (code && state) {
      processCallback(code, state);
    }
  }, [processCallback, code, state]);

  useEffect(() => {
    (async () => {
      if (isSuccess) {
        await refreshUserCRM();
        const timer = setTimeout(() => {
          router.push("/");
        }, 3000);

        return () => clearTimeout(timer);
      }

      if (isError) {
        const timer = setTimeout(() => {
          router.push("/onboarding");
        }, 3000);

        return () => clearTimeout(timer);
      }
    })();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isSuccess, isError]);

  return (
    <Layout justifyContent="center" alignItems="center" gap="$2xl">
      {isLoading ? (
        <SalesforceLoadingStep stage="connecting" />
      ) : isImporting ? (
        <SalesforceLoadingStep stage="importing" />
      ) : isSuccess ? (
        <SalesforceSuccessStep />
      ) : isError ? (
        <SalesforceErrorStep errorMessage={errorMessage} />
      ) : null}
    </Layout>
  );
}
