import { useGetSalesforceAuthUrl } from "@/api-hooks";
import { useRouter } from "expo-router";
import { useTranslation } from "react-i18next";
import { Alert } from "react-native";
import { FadeIn } from "react-native-reanimated";
import { <PERSON><PERSON>, Heading, Spinner, Text, YStack } from "tamagui";

import { Layout } from "@/components/layout/Layout";
import { AnimatedView } from "@/components/ui/AnimatedView";
import { Icon } from "@/components/ui/Icon";
import { Image } from "@/components/ui/Image";
import { useAssets } from "@/hooks/useAssets";

export default function SalesforceScreen() {
  const router = useRouter();
  const { t } = useTranslation(["screens"]);
  const { icons } = useAssets();

  const { data: authUrlData, isLoading } = useGetSalesforceAuthUrl();

  const handleConnectToSalesforce = () => {
    if (authUrlData?.authUrl) {
      router.push({
        pathname: "/salesforce-login",
        params: {
          authUrl: authUrlData.authUrl,
        },
      });
    } else {
      Alert.alert(t("screens:salesforce.error"));
    }
  };

  return (
    <Layout justifyContent="center" alignItems="center" gap="$2xl">
      {isLoading ? (
        <Spinner size="large" color="$grey" />
      ) : (
        <AnimatedView entering={FadeIn.duration(500)}>
          <Image
            source={icons.salesforce}
            width={120}
            aspectRatio={728 / 512}
            alignSelf="center"
            marginBottom="$lg"
          />

          <Heading
            fontSize={28}
            fontWeight="700"
            textAlign="center"
            marginBottom="$md"
            color="$grey"
          >
            {t("screens:salesforce.title")}
          </Heading>

          <YStack gap="$md" alignItems="center" marginBottom="$2xl">
            <Text
              fontSize={16}
              lineHeight={24}
              textAlign="center"
              color="$infoText"
              maxWidth={400}
            >
              {t("screens:salesforce.description")}
            </Text>

            <Text
              fontSize={14}
              lineHeight={20}
              textAlign="center"
              color="$infoText"
              marginTop="$sm"
            >
              {t("screens:salesforce.description2")}
            </Text>
          </YStack>

          <Button
            backgroundColor="$loginButtonBackground"
            borderColor="$loginButtonBorder"
            borderWidth={1}
            height={48}
            borderRadius={8}
            paddingHorizontal="$2xl"
            onPress={handleConnectToSalesforce}
            pressStyle={{
              scale: 0.98,
            }}
          >
            <Button.Text color="$white" fontSize={16} fontWeight="500">
              {t("screens:salesforce.cta")}
            </Button.Text>
            <Icon name="ArrowRight" size={16} color="$white" />
          </Button>
        </AnimatedView>
      )}
    </Layout>
  );
}
