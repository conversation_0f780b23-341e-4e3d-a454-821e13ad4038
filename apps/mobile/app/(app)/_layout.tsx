import { useAuth } from "@/api-client";
import { useGetUserCRM } from "@/api-hooks";
import { useSyncUserAccounts } from "@/api-hooks/useSyncUserAccounts";
import { Redirect } from "expo-router";
import { Drawer } from "expo-router/drawer";
import { useEffect } from "react";
import { Spinner, Text } from "tamagui";

import { ToggleThemeButton } from "@components/ToggleThemeButton";
import { Layout } from "@components/layout/Layout";
import { CustomDrawerContent } from "@components/navigation/drawer/CustomDrawerContent";
import { CustomDrawerMenuButton } from "@components/navigation/drawer/CustomDrawerMenuButton";
import { Header } from "@components/navigation/header/Header";
import { Icon } from "@components/ui/Icon";

export default function DrawerLayout() {
  const { authenticated, loading: isLoadingAuth } = useAuth();

  const {
    data: userCRM,
    isLoading: isLoadingUserCRM,
    isError,
  } = useGetUserCRM();

  const { mutate: syncUserAccounts } = useSyncUserAccounts();

  useEffect(() => {
    syncUserAccounts();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!authenticated && !isLoadingAuth) {
    return <Redirect href="/login" />;
  }

  if (isLoadingAuth || isLoadingUserCRM) {
    return (
      <Layout justifyContent="center" alignItems="center">
        <Spinner size="large" color="$grey" />
      </Layout>
    );
  }

  if (!userCRM && !isLoadingUserCRM) {
    return <Redirect href="/onboarding" />;
  }

  if (isError) {
    return <Redirect href="/error" />;
  }

  return (
    <Drawer
      drawerContent={(props) => <CustomDrawerContent {...props} />}
      screenOptions={{
        headerLeft: (props) => <CustomDrawerMenuButton {...props} />,
        headerTransparent: true,
        drawerType: "slide",
      }}
    >
      <Drawer.Screen
        name="index"
        options={{
          drawerLabel: "Home",
          drawerIcon: ({ size }) => <Icon name="Home" size={size} />,
          title: "Home",
          headerTransparent: true,
          headerTitle: () => <Text>{"Pearl Alpha"}</Text>,
        }}
      />
      <Drawer.Screen
        name="chat/index"
        options={{
          header: (props) => <Header {...props} withHeaderBackground />,
          headerRight: () => <ToggleThemeButton />,
        }}
      />
    </Drawer>
  );
}
