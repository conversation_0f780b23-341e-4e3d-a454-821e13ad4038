import { useAuth } from "@/api-client";
import { router } from "expo-router";
import { useState } from "react";
import { useTranslation } from "react-i18next";
import { Button, YStack, Text } from "tamagui";

import { ChatBarInput } from "@components/chat/ChatBarInput";
import { KeyboardAwareScrollBox } from "@components/layout/KeyboardAwareScrollBox";
import { Layout } from "@components/layout/Layout";
import { Image } from "@components/ui/Image";
import { useAssets } from "@hooks/useAssets";
import { useTheme } from "@hooks/useTheme";

const IMAGE_SIZE = 42;

export default function IndexScreen() {
  const { toggleTheme } = useTheme();
  const { t, i18n } = useTranslation(["screens"]);
  const { logout } = useAuth();

  const [prompt, setPrompt] = useState("");

  const { icons } = useAssets();

  const [isLoading, setIsLoading] = useState(false);

  const handleToggleLanguage = () => {
    i18n.changeLanguage(i18n.language === "fr" ? "en" : "fr");
  };

  const handleLogout = async () => {
    try {
      setIsLoading(true);
      await logout();
    } catch (error) {
      console.error(error);
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Layout
      isLoading={isLoading}
      withEdgeBottom={false}
      withEdgeTop={false}
      withHorizontalPadding={false}
      style={{ paddingTop: 0 }}
    >
      <KeyboardAwareScrollBox
        contentContainerStyle={{
          flex: 1,
          justifyContent: "center",
          paddingHorizontal: 20,
        }}
      >
        <Image
          source={icons.appIcon}
          style={{ width: IMAGE_SIZE, height: IMAGE_SIZE }}
          alignSelf="center"
        />
        <Text
          fontSize={28}
          textAlign="center"
          marginVertical="$4xl"
          marginHorizontal="$4xl"
        >
          {"What can I help you with, Erica?"}
        </Text>
        <ChatBarInput
          value={prompt}
          onChangeText={setPrompt}
          placeholder="Ask anything"
          shouldAnimateOnFocus={false}
          onSendMessage={() => {
            //TODO: implement chat redirection logic
            router.push({
              pathname: "/chat",
              params: {
                accountId: "Hello",
                prompt: prompt,
              },
            });
          }}
        />
        <YStack gap={16} marginTop="$4xl">
          <Button onPress={toggleTheme}>
            <Button.Text>{"Toggle Theme"}</Button.Text>
          </Button>
          <Text fontSize={32} textAlign="center">
            {t("screens:index.hello")}
          </Text>
          <Button onPress={handleToggleLanguage}>
            <Button.Text>{"Toggle Language"}</Button.Text>
          </Button>
          <Button onPress={handleLogout}>
            <Button.Text>{"Logout"}</Button.Text>
          </Button>
          <Button
            onPress={() => {
              router.push("/(app)/chat");
            }}
          >
            <Button.Text>{"Go to account"}</Button.Text>
          </Button>
        </YStack>
      </KeyboardAwareScrollBox>
    </Layout>
  );
}
