import structuredClone from "@ungap/structured-clone";
import { Platform } from "react-native";

// This is needed for the ai-sdk to work on Expo
// structuredClone, TextEncoderStream, TextDecoderStream are not available in the Expo runtime
// https://ai-sdk.dev/docs/getting-started/expo#polyfills

if (Platform.OS !== "web") {
  const setupPolyfills = async () => {
    const { polyfillGlobal } = await import(
      "react-native/Libraries/Utilities/PolyfillFunctions"
    );

    const { TextEncoderStream, TextDecoderStream } = await import(
      "@stardazed/streams-text-encoding"
    );

    if (!("structuredClone" in global)) {
      polyfillGlobal("structuredClone", () => structuredClone);
    }

    polyfillGlobal("TextEncoderStream", () => TextEncoderStream);
    polyfillGlobal("TextDecoderStream", () => TextDecoderStream);
  };

  setupPolyfills();
}

export {};
