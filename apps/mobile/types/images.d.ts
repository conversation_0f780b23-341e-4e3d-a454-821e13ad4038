declare module "*.svg" {
  import { FC } from "react";
  import { StyleProp, ViewStyle } from "react-native";
  import { SvgProps } from "react-native-svg";

  interface SvgStyle extends StyleProp<ViewStyle> {
    color?: string;
  }

  interface SvgViewProps extends SvgProps {
    size?: number;
    style?: SvgStyle;
  }

  const content: FC<SvgViewProps>;

  export default content;
}

declare module "*.jpg";
declare module "*.jpeg";
declare module "*.png";
