import { useEffect } from "react";
import {
  Easing,
  interpolate,
  useAnimatedStyle,
  useSharedValue,
  withRepeat,
  withTiming,
} from "react-native-reanimated";
import { GetThemeValueForKey, ViewProps } from "tamagui";

import { AnimatedView } from "@/components/ui/AnimatedView";

type PulsingCircleProps = ViewProps & {
  color?: GetThemeValueForKey<"color">;
  duration?: number;
  size?: number;
};

export const PulsingCircle = ({
  size = 18,
  duration = 650,
  color = "$infoText",
  children,
  ...props
}: PulsingCircleProps) => {
  const pulse = useSharedValue(0);

  const style = useAnimatedStyle(() => {
    return {
      transform: [
        {
          scale: interpolate(pulse.value, [0, 1], [0.6, 1]),
        },
      ],
    };
  });

  useEffect(() => {
    pulse.value = withRepeat(
      withTiming(1, { duration, easing: Easing.ease }),
      -1,
      true,
    );
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return (
    <AnimatedView
      borderRadius="$full"
      backgroundColor={color}
      width={size}
      height={size}
      alignItems="center"
      justifyContent="center"
      style={[style]}
      {...props}
    >
      {children}
    </AnimatedView>
  );
};
