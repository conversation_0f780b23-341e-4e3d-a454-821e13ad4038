import { DrawerNavigationProp } from "@react-navigation/drawer";
import { HeaderButton, HeaderButtonProps } from "@react-navigation/elements";
import {
  DrawerActions,
  ParamListBase,
  useNavigation,
} from "@react-navigation/native";

import { Icon } from "@components/ui/Icon";

export const CustomDrawerMenuButton = (props: Partial<HeaderButtonProps>) => {
  const navigation = useNavigation<DrawerNavigationProp<ParamListBase>>();

  return (
    <HeaderButton
      {...props}
      onPress={() => navigation.dispatch(DrawerActions.toggleDrawer())}
    >
      <Icon name="Menu" size={32} />
    </HeaderButton>
  );
};
