import { DrawerHeaderProps } from "@react-navigation/drawer";
import {
  Header as HeaderBase,
  HeaderO<PERSON>s,
  HeaderTitleProps as HeaderTitlePropsBase,
  getHeaderTitle,
} from "@react-navigation/elements";
import { NativeStackHeaderProps } from "@react-navigation/native-stack";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { XStack, Text, TextProps, View } from "tamagui";

import { useGetDefaultHeaderHeight } from "@/hooks/useGetDefaultHeaderHeight";
import { BackButton } from "@components/navigation/header/BackButton";
import { Image } from "@components/ui/Image";
import { useAssets } from "@hooks/useAssets";

type HeaderTitleProps = Omit<TextProps, "style"> &
  Omit<HeaderTitlePropsBase, "style"> & {
    shouldAlignCenter?: boolean;
  };

export const HeaderTitle = ({
  shouldAlignCenter,
  ...props
}: HeaderTitleProps) => (
  <XStack
    alignItems="center"
    justifyContent={shouldAlignCenter ? "center" : "flex-start"}
    marginRight="$2xl"
    marginLeft="$xs"
  >
    <Text
      fontWeight="600"
      fontSize={20}
      lineHeight={28}
      numberOfLines={1}
      textAlign={shouldAlignCenter ? "center" : "left"}
      {...props}
    >
      {props.children}
    </Text>
  </XStack>
);

export type HeaderProps = HeaderOptions &
  (DrawerHeaderProps | NativeStackHeaderProps) & {
    isModal?: boolean;
    onBackPress?: () => void;
    withHeaderBackground?: boolean;
  };

export const Header = ({
  options,
  route,
  onBackPress,
  withHeaderBackground,
  isModal,
  ...props
}: HeaderProps) => {
  const insets = useSafeAreaInsets();
  const { defaultHeaderHeight } = useGetDefaultHeaderHeight(isModal);

  const { backgrounds } = useAssets();

  const hasBack = "back" in props && !!props.back;

  const headerLeft =
    (options.headerLeft as HeaderOptions["headerLeft"]) ??
    (hasBack || onBackPress
      ? (props) =>
          onBackPress ? (
            <BackButton onPress={onBackPress} {...props} />
          ) : (
            <BackButton {...props} />
          )
      : undefined);

  return (
    <HeaderBase
      {...options}
      title={getHeaderTitle(options, route.name)}
      headerLeft={headerLeft}
      headerTitle={
        options.headerTitle
          ? options.headerTitle
          : (props) => (
              <HeaderTitle
                shouldAlignCenter={!!options.headerTitleAlign}
                {...props}
              />
            )
      }
      headerStyle={[
        {
          backgroundColor: "transparent",
        },
        options.headerStyle,
      ]}
      headerStatusBarHeight={insets.top}
      headerLeftContainerStyle={{ paddingStart: 12 }}
      headerRightContainerStyle={{ paddingEnd: 20 }}
      headerShadowVisible={false}
      headerBackground={
        withHeaderBackground
          ? () => (
              <View height={insets.top + defaultHeaderHeight}>
                <Image
                  source={backgrounds.header}
                  style={{ flex: 1 }}
                  contentFit="fill"
                />
              </View>
            )
          : undefined
      }
      {...props}
    />
  );
};
