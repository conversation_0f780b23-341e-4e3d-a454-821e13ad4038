import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withTiming,
  withSpring,
  withSequence,
} from "react-native-reanimated";
import { YStack, Text } from "tamagui";

import { Icon } from "@/components/ui/Icon";

type SalesforceErrorStepProps = {
  errorMessage: string | null;
};

export const SalesforceErrorStep = ({
  errorMessage,
}: SalesforceErrorStepProps) => {
  const { t } = useTranslation(["components"]);

  const scale = useSharedValue(0);
  const opacity = useSharedValue(0);
  const shake = useSharedValue(0);

  useEffect(() => {
    // Entrance animation
    scale.value = withSpring(1, { damping: 15, stiffness: 100 });
    opacity.value = withTiming(1, { duration: 800 });

    // Shake animation for error
    setTimeout(() => {
      shake.value = withSequence(
        withTiming(-5, { duration: 50 }),
        withTiming(5, { duration: 100 }),
        withTiming(-5, { duration: 100 }),
        withTiming(0, { duration: 50 }),
      );
    }, 300);
  }, [scale, opacity, shake]);

  const animatedContainerStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }, { translateX: shake.value }],
    opacity: opacity.value,
  }));

  return (
    <Animated.View style={animatedContainerStyle}>
      <YStack alignItems="center" gap="$lg" paddingHorizontal="$lg">
        <YStack alignItems="center" marginBottom="$md">
          <Icon name="XCircle" size={64} color="$red10" />
        </YStack>

        <Text
          fontSize={24}
          fontWeight="700"
          textAlign="center"
          color="$red10"
          marginBottom="$md"
        >
          {t("components:salesforce.errorStep.title")}
        </Text>

        <Text
          fontSize={16}
          lineHeight={24}
          textAlign="center"
          color="$infoText"
          marginBottom="$lg"
          maxWidth={400}
        >
          {errorMessage || t("components:salesforce.errorStep.description")}
        </Text>

        <Text fontSize={14} textAlign="center" color="$infoText" opacity={0.8}>
          {t("components:salesforce.errorStep.redirect")}
        </Text>
      </YStack>
    </Animated.View>
  );
};
