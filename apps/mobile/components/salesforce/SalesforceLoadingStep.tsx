import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import Animated, {
  useSharedValue,
  useAnimatedStyle,
  withRepeat,
  withTiming,
  interpolate,
} from "react-native-reanimated";
import { YStack, XStack, Text } from "tamagui";

import { Icon } from "@/components/ui/Icon";
import { Image } from "@/components/ui/Image";
import { useAssets } from "@/hooks/useAssets";

type SalesforceLoadingStepProps = {
  stage?: "connecting" | "importing";
};

export const SalesforceLoadingStep = ({
  stage = "connecting",
}: SalesforceLoadingStepProps) => {
  const { icons } = useAssets();
  const rotation = useSharedValue(0);
  const scale = useSharedValue(1);
  const { t } = useTranslation(["components"]);

  useEffect(() => {
    // Continuous rotation for the syncing icon
    rotation.value = withRepeat(withTiming(360, { duration: 2000 }), -1, false);

    // Pulse animation for scale
    scale.value = withRepeat(withTiming(1.1, { duration: 1000 }), -1, true);
  }, [rotation, scale]);

  const animatedSyncStyle = useAnimatedStyle(() => ({
    transform: [
      { rotate: `${rotation.value}deg` },
      { scale: interpolate(scale.value, [1, 1.1], [1, 1.05]) },
    ],
  }));

  const animatedSalesforceStyle = useAnimatedStyle(() => ({
    transform: [{ scale: stage === "importing" ? scale.value : 1 }],
  }));

  return (
    <YStack alignItems="center" gap="$lg">
      <XStack alignItems="center" gap="$4xl" marginBottom="$lg">
        <YStack alignItems="center" justifyContent="center">
          <Image source={icons.appIcon} width={64} height={64} />
        </YStack>

        <Animated.View style={[animatedSyncStyle, { opacity: 0.7 }]}>
          <Icon name="RefreshCw" size={24} color="$grey" />
        </Animated.View>

        <Animated.View style={animatedSalesforceStyle}>
          <Image
            source={icons.salesforce}
            height={64}
            aspectRatio={728 / 512}
          />
        </Animated.View>
      </XStack>

      <Text
        fontSize={18}
        fontWeight="600"
        textAlign="center"
        color="$grey"
        marginBottom="$sm"
      >
        {stage === "connecting"
          ? t("components:salesforce.loadingStep.connecting.title")
          : t("components:salesforce.loadingStep.importing.title")}
      </Text>

      <Text fontSize={14} textAlign="center" color="$infoText" opacity={0.8}>
        {stage === "connecting"
          ? t("components:salesforce.loadingStep.connecting.description")
          : t("components:salesforce.loadingStep.importing.description")}
      </Text>
    </YStack>
  );
};
