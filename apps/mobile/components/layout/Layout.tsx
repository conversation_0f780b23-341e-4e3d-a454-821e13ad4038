import { HeaderShownContext } from "@react-navigation/elements";
import { ImageProps } from "expo-image";
import { useContext } from "react";
import { ViewStyle } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";
import { GetThemeValueForKey, Overlay, Spinner, getTokens } from "tamagui";

import { Background } from "@components/layout/Background";
import { AnimatedView, AnimatedViewProps } from "@components/ui/AnimatedView";
import { useAssets } from "@hooks/useAssets";
import { useGetDefaultHeaderHeight } from "@hooks/useGetDefaultHeaderHeight";
import { Spacing } from "@theme/tokens";

export interface LayoutProps
  extends Omit<AnimatedViewProps, "backgroundColor"> {
  backgroundColor?: GetThemeValueForKey<"color">;
  backgroundImageSource?: ImageProps["source"];
  isLoading?: boolean;
  isModal?: boolean;
  withBackground?: boolean;
  withDefaultPaddingTop?: boolean;
  withEdgeBottom?: boolean;
  withEdgeTop?: boolean;
  withHeader?: boolean;
  withHorizontalPadding?: boolean;
}

export const Layout = ({
  backgroundColor = "$background",
  withBackground = true,
  backgroundImageSource,
  withHorizontalPadding = true,
  withEdgeTop,
  withEdgeBottom = true,
  withDefaultPaddingTop = true,
  withHeader = false,
  children,
  isLoading,
  isModal,
  style,
  ...props
}: LayoutProps) => {
  const { backgrounds } = useAssets();
  const insets = useSafeAreaInsets();
  const { defaultHeaderHeight } = useGetDefaultHeaderHeight(isModal);
  const isParentHeaderShown = useContext(HeaderShownContext);

  // Fix for the SafeAreaView issue
  // https://github.com/th3rdwave/react-native-safe-area-context/issues/226
  const safeAreaPaddings: ViewStyle = {
    paddingTop: isParentHeaderShown
      ? defaultHeaderHeight
      : withEdgeTop === true || withEdgeTop === undefined
        ? insets.top
        : 0,
    paddingBottom: withEdgeBottom ? insets.bottom : 0,
    // TODO: replace with getToken('$layout')
    paddingHorizontal: withHorizontalPadding ? 20 : 0,
  };

  const stylePaddingTop =
    (getTokens().space[
      (props.paddingTop as Spacing) || (withDefaultPaddingTop ? "$md" : "$none")
    ]?.val as number) || 0;

  const backgroundImage = backgroundImageSource
    ? backgroundImageSource
    : backgrounds.default;

  return (
    <>
      <AnimatedView
        flex={1}
        backgroundColor={backgroundColor}
        style={[
          safeAreaPaddings,
          {
            paddingTop:
              (safeAreaPaddings.paddingTop as number) + stylePaddingTop || 0,
          },
          style,
        ]}
        {...props}
      >
        {withBackground ? (
          <Background backgroundImageSource={backgroundImage} />
        ) : null}
        {children}
      </AnimatedView>
      {isLoading ? (
        <Overlay
          backgroundColor="$transparent30"
          justifyContent="center"
          alignItems="center"
        >
          <Spinner size="large" color="$grey" />
        </Overlay>
      ) : null}
    </>
  );
};
