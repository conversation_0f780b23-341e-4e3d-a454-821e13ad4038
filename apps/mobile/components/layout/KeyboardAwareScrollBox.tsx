import { ComponentProps, forwardRef } from "react";
import { ScrollView } from "react-native";
import { KeyboardAwareScrollView } from "react-native-keyboard-controller";

type KeyboardAwareScrollBoxProps = ComponentProps<
  typeof KeyboardAwareScrollView
>;

// eslint-disable-next-line react/display-name
export const KeyboardAwareScrollBox = forwardRef<
  ScrollView,
  KeyboardAwareScrollBoxProps
>(({ children, ...props }, ref) => (
  <KeyboardAwareScrollView
    style={{ flex: 1 }}
    showsVerticalScrollIndicator={false}
    keyboardShouldPersistTaps="handled"
    ref={ref}
    {...props}
  >
    {children}
  </KeyboardAwareScrollView>
));
