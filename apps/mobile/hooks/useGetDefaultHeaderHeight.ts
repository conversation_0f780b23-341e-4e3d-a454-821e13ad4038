import { getDefaultHeaderHeight } from "@react-navigation/elements";
import { useMemo } from "react";
import { useWindowDimensions } from "react-native";
import { useSafeAreaInsets } from "react-native-safe-area-context";

import { MODAL_STATUS_BAR_HEIGHT } from "@constants/layout";

export const useGetDefaultHeaderHeight = (isModal?: boolean) => {
  const insets = useSafeAreaInsets();
  const { width, height } = useWindowDimensions();

  const defaultHeaderHeight = useMemo(
    () =>
      getDefaultHeaderHeight(
        { width, height },
        false,
        isModal ? MODAL_STATUS_BAR_HEIGHT : insets.top,
      ),
    [width, height, insets.top, isModal],
  );

  return { defaultHeaderHeight };
};
