import { TokenStorageAdapter } from "@/api-client";

import { Key, Storage } from "@utils/storage";

class TokenStorage implements TokenStorageAdapter {
  private accessTokenKey: Key = "accessToken";
  private refreshTokenKey: Key = "refreshToken";
  getAccessToken(): string | null {
    return Storage.getString(this.accessTokenKey) ?? null;
  }
  getRefreshToken(): string | null {
    return Storage.getString(this.refreshTokenKey) ?? null;
  }
  saveTokens(accessToken: string, refreshToken: string): void {
    Storage.set(this.accessTokenKey, accessToken);
    Storage.set(this.refreshTokenKey, refreshToken);
  }
  clearTokens(): void {
    Storage.del(this.accessTokenKey);
    Storage.del(this.refreshTokenKey);
  }
}

export const tokenStorage = new TokenStorage();
