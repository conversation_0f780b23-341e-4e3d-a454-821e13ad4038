import { ApiClient } from "@/api-client";
import { fetch } from "expo/fetch";

import { API_URL } from "@/constants/environment";
import { tokenStorage } from "@services/api/tokenStorage";

export const apiClient = new ApiClient(
  API_URL,
  tokenStorage,
  // TODO: find a way to use the fetch from expo/fetch and type this correctly
  fetch as (url: string, init?: RequestInit) => Promise<Response>,
);
