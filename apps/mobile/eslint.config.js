const { defineConfig } = require("eslint/config");
const expoConfig = require("eslint-config-expo/flat");
const eslintPluginPrettierRecommended = require("eslint-plugin-prettier/recommended");
const eslintPluginReactNative = require("eslint-plugin-react-native");
const typescriptSortKeys = require("eslint-plugin-typescript-sort-keys");

module.exports = defineConfig([
  expoConfig,
  eslintPluginPrettierRecommended,
  {
    ignores: [
      "dist/*",
      "/.expo",
      "/.expo-shared",
      "/.tamagui",
      "/.vscode",
      "/.github",
      "/.husky",
      "scripts",
      "node_modules",
      "android",
      "ios",
      ".vscode",
      "assets",
      ".github",
      "patches",
      "assets",
      "expo-env.d.ts",
      "web-build/*",
      "ios/*",
      "android/*",
      "*.jks",
      "*.p8",
      "*.p12",
      "*.key",
      "*.mobileprovision",
      "*.orig.*",
      "*.app",
      "*.apk",
      "*.tar.gz",
      "*.aab",
      "*.ipa",
      "*.zip",
      ".gitignore",
      ".eslintrc.js",
      ".prettierignore",
      ".prettierrc.js",
      "yarn.lock",
    ],
    plugins: {
      "typescript-sort-keys": typescriptSortKeys,
      "react-native": eslintPluginReactNative,
    },
    rules: {
      curly: ["error", "all"],

      "no-console": [
        "warn",
        {
          allow: ["error"],
        },
      ],

      "no-unreachable": "error",
      "no-shadow": "off",
      "import/no-named-as-default": "off",
      "import/namespace": [
        "error",
        {
          allowComputed: true,
        },
      ],
      "import/order": [
        "error",
        {
          alphabetize: {
            order: "asc",
          },

          groups: [
            ["builtin", "external"],
            ["unknown"],
            ["internal"],
            ["parent"],
            ["sibling", "index"],
          ],

          "newlines-between": "always",
        },
      ],

      "react/jsx-key": "error",
      "react/jsx-no-literals": "error",
      "react/jsx-boolean-value": ["error", "never"],

      "react/jsx-curly-brace-presence": [
        "error",
        {
          props: "never",
          children: "ignore",
        },
      ],

      "react/no-unstable-nested-components": "off",
      "@typescript-eslint/no-shadow": "off",

      "typescript-sort-keys/interface": [
        "error",
        "asc",
        {
          requiredFirst: true,
        },
      ],

      "react/jsx-no-useless-fragment": "error",

      "react/jsx-no-leaked-render": [
        "error",
        {
          validStrategies: ["ternary", "coerce"],
        },
      ],

      "react-hooks/exhaustive-deps": "warn",
      "no-empty": "warn",
      "import/no-anonymous-default-export": "error",
      "react-native/no-unused-styles": "error",

      // TODO: Add these rules when we have a query client
      // '@tanstack/query/exhaustive-deps': 'error',
      // '@tanstack/query/no-rest-destructuring': 'warn',
      // '@tanstack/query/stable-query-client': 'error',

      "no-restricted-syntax": [
        "error",
        {
          selector:
            'MemberExpression[object.name="router"][property.name="navigate"]',
          message:
            "Please use router.push() instead of router.navigate(). The behaviour is the same but push is more explicit",
        },
      ],

      "no-restricted-imports": [
        "error",
        {
          paths: [
            {
              name: "tamagui",
              importNames: ["useTheme"],
              message: "Please use local hook instead.",
            },

            {
              name: "react",
              importNames: ["default"],
              message:
                "Prefer destructuring imports to avoid cluttering the code.",
            },
            {
              name: "i18next",
              importNames: ["t"],
              message:
                "Please use useTranslation hook from react-i18next instead of importing t directly from i18next.",
            },
          ],
        },
      ],

      "no-void": "off",
    },
  },
]);
