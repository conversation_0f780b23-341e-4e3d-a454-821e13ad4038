import {
  atomWithStorage as Jo<PERSON>AtomWithStorage,
  createJSONStorage,
} from "jotai/utils";

import { Storage, Key } from "@utils/storage";

const getItem = <T>(key: string): T | null => {
  const value = Storage.getString(key as Key);

  return value ? JSON.parse(value) : null;
};

const setItem = <T>(key: string, value: T) => {
  Storage.set(key as Key, JSON.stringify(value));
};

const removeItem = (key: string) => {
  Storage.del(key as Key);
};

export const atomWithStorage = <T>(key: Key, initialValue: T) =>
  JotaiAtomWithStorage<T>(
    key,
    initialValue,
    createJSONStorage<T>(() => ({
      getItem,
      setItem,
      removeItem,
      clearAll: Storage.clear,
    })),
    { getOnInit: true },
  );
