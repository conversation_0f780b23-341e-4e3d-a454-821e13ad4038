import { FlatCompat } from "@eslint/eslintrc";
import pluginNoRelativeImports from "eslint-plugin-no-relative-import-paths";
import pluginReact from "eslint-plugin-react";

import rootConfig from "../../eslint.config.mjs"; // eslint-disable-line no-relative-import-paths/no-relative-import-paths

const compat = new FlatCompat({
  baseDirectory: import.meta.dirname,
});

const eslintConfig = [
  ...rootConfig,
  pluginReact.configs.flat.recommended,
  {
    plugins: {
      "no-relative-import-paths": pluginNoRelativeImports,
    },
    rules: {
      "no-relative-import-paths/no-relative-import-paths": "error",
    },
  },
  ...compat.config({
    extends: ["next/core-web-vitals", "next/typescript"],
    rules: {
      "react/no-unescaped-entities": "off",
    },
    ignorePatterns: ["node_modules/", ".next/", "dist/", "build/"],
  }),
];

export default eslintConfig;
