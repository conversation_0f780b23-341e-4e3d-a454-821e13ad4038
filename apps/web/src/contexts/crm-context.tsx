import { User<PERSON><PERSON>, useGetUserCRM } from "@/api-hooks";
import { ReactNode, createContext, useContext } from "react";

type CRMContextType = {
  userCRM: UserCRM | null;
  isLoading: boolean;
  isError: boolean;
  refreshUserCRM: () => void;
};

const CRMContext = createContext<CRMContextType | undefined>(undefined);

export function CRMProvider({ children }: { children: ReactNode }) {
  const { data, isLoading, isError, refetch } = useGetUserCRM();

  return (
    <CRMContext.Provider
      value={{
        userCRM: data ?? null,
        isLoading,
        isError,
        refreshUserCRM: refetch,
      }}
    >
      {children}
    </CRMContext.Provider>
  );
}

export function useCRM() {
  const context = useContext(CRMContext);
  if (context === undefined) {
    throw new Error("useCRM must be used within a CRMProvider");
  }
  return context;
}
