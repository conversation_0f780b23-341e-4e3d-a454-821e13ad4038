import { AuthProvider as BaseAuthProvider } from "@/api-client";
import { queryClient } from "@/api-hooks";
import { useRouter } from "next/navigation";

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
  const router = useRouter();
  const handleLogin = () => {
    router.push("/");
  };
  const handleLogout = () => {
    queryClient.clear();
    router.push("/login/email");
  };
  return (
    <BaseAuthProvider onLogin={handleLogin} onLogout={handleLogout}>
      {children}
    </BaseAuthProvider>
  );
};
