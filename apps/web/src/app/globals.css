@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter);
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: rgb(246, 247, 253);
  --foreground: rgb(3, 7, 18);
  --card: rgb(255, 255, 255);
  --card-foreground: rgb(3, 7, 18);
  --popover: rgb(255, 255, 255);
  --popover-foreground: rgb(3, 7, 18);
  --primary: rgb(24, 24, 27);
  --primary-foreground: rgb(249, 250, 251);
  --secondary: rgb(231, 235, 241);
  --secondary-foreground: rgb(16, 24, 40);
  --muted: rgb(243, 244, 246);
  --muted-foreground: rgb(106, 114, 130);
  --accent: rgb(243, 244, 246);
  --accent-foreground: rgb(16, 24, 40);
  --destructive: rgb(231, 0, 11);
  --border: rgb(229, 231, 235);
  --input: rgb(255, 255, 255);
  --ring: rgb(153, 161, 175);
  --chart-1: rgb(245, 74, 0);
  --chart-2: rgb(0, 150, 137);
  --chart-3: rgb(16, 78, 100);
  --chart-4: rgb(255, 186, 0);
  --chart-5: rgb(253, 154, 0);
  --sidebar: rgb(238, 240, 252);
  --sidebar-foreground: rgb(3, 7, 18);
  --sidebar-primary: rgb(24, 24, 27);
  --sidebar-primary-foreground: rgb(249, 250, 251);
  --sidebar-accent: rgba(255, 255, 255, 0.7);
  --sidebar-accent-foreground: rgb(16, 24, 40);
  --sidebar-accent-rgb:
    255, 255, 255; /* Need RGB for gradient otherwise this doesn't work) */
  --sidebar-border: rgb(229, 231, 235);
  --sidebar-ring: rgb(153, 161, 175);
  --logo-filter: brightness(0.1);
  --user-message-background: rgba(216, 218, 234);
  --user-message-foreground: rgba(3, 7, 18);
  --incoming-message-background: rgb(238, 240, 252);
  --incoming-message-foreground: rgb(3, 7, 18);
}

.dark {
  --background: rgba(36, 38, 41, 1);
  --foreground: rgba(250, 250, 250, 1);
  --card: rgba(9, 10, 10, 1);
  --card-foreground: rgba(250, 250, 250, 1);
  --popover: rgba(9, 10, 10, 1);
  --popover-foreground: rgba(250, 250, 250, 1);
  --primary: rgba(250, 250, 250, 1);
  --primary-foreground: rgba(23, 23, 23, 1);
  --secondary: rgb(79, 82, 87);
  --secondary-foreground: rgba(250, 250, 250, 1);
  --muted: rgba(27, 28, 30, 1);
  --muted-foreground: rgba(163, 163, 163, 1);
  --accent: rgba(38, 40, 43, 1);
  --accent-foreground: rgba(250, 250, 250, 1);
  --destructive: rgba(153, 40, 40, 1);
  --destructive-foreground: rgba(250, 250, 250, 1);
  --border: rgba(32, 33, 36, 1);
  --input: rgb(75, 78, 85);
  --ring: rgba(212, 212, 212, 1);
  --chart-1: rgba(38, 128, 217, 1);
  --chart-2: rgba(46, 163, 115, 1);
  --chart-3: rgba(204, 112, 46, 1);
  --chart-4: rgba(179, 77, 230, 1);
  --chart-5: rgba(204, 46, 92, 1);
  --sidebar: rgba(24, 25, 26, 1);
  --sidebar-foreground: rgba(244, 245, 245, 1);
  --sidebar-primary: rgba(250, 250, 250, 1);
  --sidebar-primary-foreground: rgba(255, 255, 255, 1);
  --sidebar-accent: rgba(39, 40, 42, 1);
  --sidebar-accent-foreground: rgba(244, 245, 245, 1);
  --sidebar-accent-rgb: 39, 40, 42;
  --sidebar-border: rgba(39, 40, 42, 1);
  --sidebar-ring: rgba(244, 245, 245, 1);
  --logo-filter: none;
  --user-message-background: rgba(79, 82, 87);
  --user-message-foreground: rgb(255, 255, 255);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    background-color: var(--sidebar) !important;
    @apply text-foreground;
  }
}

.dark [data-slot="dropdown-menu-content"] {
  background: rgba(38, 40, 43, 1) !important;
}

.custom-bg {
  background-size: 100% 100%;
  background-position:
    0px 0px,
    0px 0px,
    0px 0px,
    0px 0px;
  background-image:
    linear-gradient(0deg, #e4e6f1 50%, #1a1b1f00 100%),
    radial-gradient(80% 50% at 104% 12%, #ffac8663 40%, #073aff00 100%),
    radial-gradient(74% 50% at 0% 14%, #97a0d36e 40%, #073aff00 100%),
    linear-gradient(125deg, #e4e6f1 0%, #e4e6f1 100%);
}

.dark .custom-bg {
  background-size: 100% 100%;
  background-position:
    0px 0px,
    0px 0px,
    0px 0px,
    0px 0px;
  background-image:
    linear-gradient(0deg, #1a1b1f 50%, #1a1b1f00 100%),
    radial-gradient(80% 50% at 104% 12%, #ffac8652 40%, #073aff00 100%),
    radial-gradient(74% 50% at 0% 14%, #97a0d357 40%, #073aff00 100%),
    linear-gradient(125deg, #1a1b1f 0%, #1a1b1f 100%);
}

.sidebar-menu-button-gradient {
  background: linear-gradient(
    to right,
    rgba(var(--sidebar-accent-rgb), 1) 0%,
    rgba(var(--sidebar-accent-rgb), 0) 100%
  );
}
