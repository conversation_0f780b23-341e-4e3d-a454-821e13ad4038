"use client";

import { useSalesforceCallback } from "@/api-hooks";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect } from "react";

import { SalesforceCallbackContent } from "@/web/components/salesforce/salesforce-callback-content";
import { useCRM } from "@/web/contexts/crm-context";

export default function SalesforceCallbackPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { refreshUserCRM } = useCRM();

  const code = searchParams.get("code");
  const state = searchParams.get("state");

  const {
    isLoading,
    isImporting,
    isSuccess,
    isError,
    errorMessage,
    processCallback,
  } = useSalesforceCallback();

  useEffect(() => {
    if (code && state) {
      processCallback(code, state);
    }
  }, [code, state, processCallback]);

  useEffect(() => {
    if (isSuccess) {
      const timer = setTimeout(() => {
        refreshUserCRM();
        router.push("/");
      }, 5000);

      return () => clearTimeout(timer);
    }

    if (isError) {
      const timer = setTimeout(() => {
        router.push("/onboarding");
      }, 3000);

      return () => clearTimeout(timer);
    }
  }, [isSuccess, isError, router, refreshUserCRM]);

  return (
    <SalesforceCallbackContent
      isLoading={isLoading}
      isImporting={isImporting}
      isSuccess={isSuccess}
      isError={isError}
      errorMessage={errorMessage}
    />
  );
}
