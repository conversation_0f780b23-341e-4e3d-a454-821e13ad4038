"use client";

import { useSyncUserAccounts } from "@/api-hooks/useSyncUserAccounts";
import { useEffect, Fragment } from "react";

import CRMRequiredRoute from "@/web/components/crm-required-route";
import Sidebar from "@/web/components/sidebar/app-sidebar";
import {
  SidebarInset,
  SidebarProvider,
  SidebarTrigger,
} from "@/web/components/ui/sidebar";
import { Separator } from "@/web/components/ui/separator";

function SyncUserAccounts({ children }: { children: React.ReactNode }) {
  const { mutate } = useSyncUserAccounts();

  // Trigger synchronization only once when the component mounts
  useEffect(() => {
    mutate();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  return <>{children}</>;
}

export default function Layout({ children }: { children: React.ReactNode }) {
  return (
    <CRMRequiredRoute>
      <SyncUserAccounts>
        <SidebarProvider>
          <Sidebar />
          <SidebarInset>
            <header className="bg-background/40 sticky top-0 z-20 flex h-16 shrink-0 items-center gap-2 rounded-t-xl border-b backdrop-blur-[20px]">
              <div className="flex items-center gap-2 px-4">
                <SidebarTrigger className="-ml-1" />
                <Separator
                  orientation="vertical"
                  className="mr-2 data-[orientation=vertical]:h-4"
                />
              </div>
            </header>
            {children}
          </SidebarInset>
        </SidebarProvider>
      </SyncUserAccounts>
    </CRMRequiredRoute>
  );
}
