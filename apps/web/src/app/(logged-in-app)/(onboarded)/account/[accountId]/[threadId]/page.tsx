"use client";

import { useParams } from "next/navigation";

import Chat<PERSON>ontainer from "@/web/components/chat/chat-container";
import { SidebarTrigger } from "@/web/components/ui/sidebar";

export default function Thread() {
  const { accountId, threadId } = useParams();

  return (
    <>
      <SidebarTrigger className="md:hidden" />
      <ChatContainer
        threadId={threadId as string}
        accountId={accountId as string}
      />
    </>
  );
}
