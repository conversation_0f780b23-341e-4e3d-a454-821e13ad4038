"use client";

import { use<PERSON>ara<PERSON>, useRouter } from "next/navigation";

import ChatContainer from "@/web/components/chat/chat-container";
import { SidebarTrigger } from "@/web/components/ui/sidebar";

export default function Account() {
  const { accountId } = useParams();

  const router = useRouter();

  const handleNewThread = (threadId: string) => {
    router.push(`/account/${accountId}/${threadId}`);
  };

  return (
    <>
      <SidebarTrigger className="md:hidden" />
      <ChatContainer
        threadId={undefined}
        accountId={accountId as string}
        onNewThread={handleNewThread}
      />
    </>
  );
}
