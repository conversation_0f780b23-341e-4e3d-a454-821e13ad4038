"use client";

import { useGetUserAccounts } from "@/api-hooks";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

export default function Account() {
  const router = useRouter();
  const { data: accounts } = useGetUserAccounts();

  useEffect(() => {
    if (accounts && accounts.length > 0) {
      // Redirect to the first account's page if available
      const firstAccount = accounts[0];
      router.push(`/account/${firstAccount.crmId}`);
    }
  }, [accounts, router]);

  return null;
}
