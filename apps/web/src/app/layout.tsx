import "@/web/app/globals.css";
import { Inter } from "next/font/google";

import type { Metadata } from "next";

import Providers from "@/web/components/providers";

export const metadata: Metadata = {
  title: "<PERSON>",
};

const inter = Inter({ subsets: ["latin"] });

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`antialiased ${inter.className}`}>
        <Providers>{children}</Providers>
      </body>
    </html>
  );
}
