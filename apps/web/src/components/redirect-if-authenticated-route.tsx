"use client";

import { useAuth } from "@/api-client";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

import Loader from "@/web/components/loader";

export default function RedirectIfAuthenticatedRoute({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const router = useRouter();
  const { authenticated, loading } = useAuth();

  useEffect(() => {
    if (!loading && authenticated) {
      router.push("/");
    }
  }, [loading, authenticated, router]);

  if (loading) {
    return <Loader />;
  }
  if (authenticated) {
    return null; // Don't render anything while redirecting
  }

  return <>{children}</>;
}
