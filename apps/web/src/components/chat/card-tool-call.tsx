import { ToolInvocation } from "ai";

import { Button } from "@/web/components/ui/button";

export default function CardToolCall({
  toolInvocation,
  addToolResult,
}: {
  toolInvocation: ToolInvocation;
  addToolResult: (obj: {
    toolCallId: string;
    result: { action: "continue" | "abort" };
  }) => void;
}) {
  const { toolCallId, toolName, args, state } = toolInvocation;
  return state === "call" ? (
    <PendingToolCall
      key={toolCallId}
      handleContinue={() =>
        addToolResult({
          toolCallId: toolCallId,
          result: { action: "continue" },
        })
      }
      handleAbort={() =>
        addToolResult({
          toolCallId: toolCallId,
          result: { action: "abort" },
        })
      }
      toolName={toolName}
      toolArgs={args}
    />
  ) : null; // TODO: handle tool success/failure
}

function PendingToolCall({
  handleContinue,
  handleAbort,
  toolName,
  toolArgs,
}: {
  handleContinue: () => void;
  handleAbort: () => void;
  toolName: string;
  toolArgs: object;
}) {
  return (
    <div className="rounded-md bg-yellow-100 p-4">
      <h3 className="font-semibold">Pending Tool Call</h3>
      <p>
        Tool Name: <strong>{toolName}</strong>
      </p>
      <pre className="rounded bg-gray-100 p-2">
        {JSON.stringify(toolArgs, null, 2)}
      </pre>
      <Button onClick={handleContinue}>Ok</Button>
      <Button variant="secondary" className="ml-2" onClick={handleAbort}>
        Abort
      </Button>
    </div>
  );
}
