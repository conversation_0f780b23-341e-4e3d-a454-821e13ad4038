"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";

import Loader from "@/web/components/loader";
import { useIsAdmin } from "@/web/hooks/use-is-admin";

export default function AdminOnlyRoute({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const router = useRouter();
  const { isAdmin, isLoadingIsAdmin } = useIsAdmin();

  useEffect(() => {
    if (!isLoadingIsAdmin && !isAdmin) {
      router.push("/");
    }
  }, [isLoadingIsAdmin, isAdmin, router]);

  if (isLoadingIsAdmin) {
    return <Loader />;
  }
  if (!isAdmin) {
    return null; // Don't render anything while redirecting
  }

  return <>{children}</>;
}
