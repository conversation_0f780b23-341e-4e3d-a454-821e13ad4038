"use client";

import { useRouter } from "next/navigation";
import { ReactNode, useEffect } from "react";

import Loader from "@/web/components/loader";
import { useCRM } from "@/web/contexts/crm-context";

interface CRMNotConfiguredProps {
  children: ReactNode;
  redirectTo?: string;
}

export default function RedirectIfCRMSetRoute({
  children,
  redirectTo = "/",
}: CRMNotConfiguredProps) {
  const { userCRM, isLoading } = useCRM();
  const router = useRouter();

  useEffect(() => {
    if (!isLoading && userCRM) {
      router.push(redirectTo);
    }
  }, [userCRM, isLoading, router, redirectTo]);

  if (isLoading) {
    return <Loader />;
  }

  if (userCRM) {
    // wait for redirect to redirectTo
    return <Loader />;
  }

  return <>{children}</>;
}
