"use client";

import { Calendar, FileText, Globe, House, Users } from "lucide-react";
import { usePathname, useRouter } from "next/navigation";

import {
  SidebarGroup,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "@/web/components/ui/sidebar";
import { useIsAdmin } from "@/web/hooks/use-is-admin";

const sidebarMainNav = [
  {
    name: "Home",
    url: "/home",
    icon: House,
  },
  {
    name: "Calendar",
    url: "/calendar",
    icon: Calendar,
  },
  {
    name: "Territory",
    url: "/territory",
    icon: Globe,
  },
  {
    name: "Documents",
    url: "/documents",
    icon: FileText,
  },
];

export default function MenuItems() {
  const pathname = usePathname();
  const router = useRouter();

  const { isAdmin } = useIsAdmin();

  return (
    <SidebarGroup className="group-data-[collapsible=icon]:hidden">
      <SidebarMenu>
        {sidebarMainNav.map((item) => {
          const Icon = item.icon;
          return (
            <SidebarMenuItem key={item.name}>
              <SidebarMenuButton
                asChild
                onClick={() => router.push(item.url)}
                isActive={pathname.startsWith(item.url)}
                className="cursor-pointer"
              >
                <span>
                  <Icon />
                  {item.name}
                </span>
              </SidebarMenuButton>
            </SidebarMenuItem>
          );
        })}
        {isAdmin ? (
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              onClick={() => router.push("/members")}
              isActive={pathname.startsWith("/members")}
              className="cursor-pointer"
            >
              <span>
                <Users />
                Members
              </span>
            </SidebarMenuButton>
          </SidebarMenuItem>
        ) : null}
      </SidebarMenu>
    </SidebarGroup>
  );
}
