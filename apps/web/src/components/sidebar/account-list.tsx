"use client";

import { useGetUserAccounts } from "@/api-hooks";

import Loader from "@/web/components/loader";
import AccountWithThreadsItem from "@/web/components/sidebar/account-with-threads-item";
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
} from "@/web/components/ui/sidebar";

export default function AccountList() {
  const {
    data: accounts,
    isError: isErrorAccounts,
    isLoading: isLoadingAccounts,
  } = useGetUserAccounts();

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Accounts</SidebarGroupLabel>
      {isLoadingAccounts ? (
        <Loader />
      ) : (
        <SidebarMenu>
          {accounts?.map((account) => (
            <AccountWithThreadsItem key={account.crmId} account={account} />
          ))}
        </SidebarMenu>
      )}
      {!isLoadingAccounts && isErrorAccounts ? (
        <div>Error loading accounts</div>
      ) : null}
    </SidebarGroup>
  );
}
