"use client";

import { useAuth } from "@/api-client";
import { useRouter } from "next/navigation";
import { useEffect } from "react";

import Loader from "@/web/components/loader";

export default function AuthenticatedRoute({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const router = useRouter();
  const { loading, authenticated } = useAuth();

  useEffect(() => {
    if (!loading && !authenticated) {
      router.push("/login/email");
    }
  }, [loading, authenticated, router]);

  if (loading) {
    return <Loader />;
  }
  if (!authenticated) {
    return null; // Don't render anything while redirecting
  }

  return <>{children}</>;
}
