"use client";

import { ApiClientProvider } from "@/api-client";
import { QueryClientProvider } from "@/api-hooks";

import { AuthProvider } from "@/web/contexts/auth-context";
import { apiClient } from "@/web/lib/api-client";
import { tokenStorage } from "@/web/lib/token-storage";

export default function Providers({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <ApiClientProvider apiClient={apiClient} tokenStorage={tokenStorage}>
      <QueryClientProvider>
        <AuthProvider>{children}</AuthProvider>
      </QueryClientProvider>
    </ApiClientProvider>
  );
}
