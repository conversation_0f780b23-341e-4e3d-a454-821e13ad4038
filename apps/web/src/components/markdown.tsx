import BaseMarkdown from "react-markdown";

export default function Markdown({
  content,
}: {
  content: string | null | undefined;
}) {
  return (
    <BaseMarkdown
      components={{
        ul: ({ children }) => (
          <ul className="list-inside list-disc">{children}</ul>
        ),
        ol: ({ children }) => (
          <ol className="list-inside list-decimal">{children}</ol>
        ),
        li: ({ children }) => <li className="ml-4">{children}</li>,
      }}
    >
      {content}
    </BaseMarkdown>
  );
}
