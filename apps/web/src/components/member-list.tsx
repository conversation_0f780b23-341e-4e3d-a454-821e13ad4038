"use client";

import { MemberProfile, useGetMemberProfiles } from "@/api-hooks";
import { Search } from "lucide-react";
import { useMemo, useState } from "react";

import { Input } from "@/web/components/ui/input";
import { cn } from "@/web/lib/utils";

export default function MemberList() {
  const { data: memberProfiles, isLoading: isLoadingMembers } =
    useGetMemberProfiles();
  const [searchQuery, setSearchQuery] = useState("");

  const filteredMembers = useMemo(() => {
    if (!memberProfiles?.members || !searchQuery.trim()) {
      return memberProfiles?.members || [];
    }
    const query = searchQuery.toLowerCase();
    return memberProfiles.members.filter((member) => {
      const fullName = `${member.firstName} ${member.lastName}`.toLowerCase();
      const email = member.email?.toLowerCase() || "";

      return fullName.includes(query) || email.includes(query);
    });
  }, [memberProfiles?.members, searchQuery]);

  return (
    <div className="p-6">
      <div className="sm:flex sm:items-center">
        <div className="sm:flex-auto">
          <div className="flex items-center gap-3">
            <h1 className="text-2xl font-semibold">Members</h1>
            {isLoadingMembers ? null : (
              <div className="bg-primary/10 text-primary rounded-full px-2 py-1 text-xs font-medium">
                {memberProfiles?.members.length}
              </div>
            )}
          </div>
        </div>
        <div className="mt-4 flex items-center gap-4 sm:ml-16 sm:mt-0">
          <div className="relative w-full sm:w-auto">
            <Search className="text-muted-foreground absolute left-2.5 top-2.5 size-4" />
            <Input
              type="search"
              placeholder="Search members..."
              className="dark:bg-muted/100 w-full pl-8 sm:w-[200px] md:w-[240px]"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>
        </div>
      </div>
      <div className="mt-8 flow-root">
        <div className="overflow-x-auto">
          <div className="inline-block min-w-full align-middle">
            <table className="divide-border w-full divide-y">
              <Header />
              <tbody className="divide-border divide-y">
                {filteredMembers.map((member) => (
                  <MemberRow key={member.id} member={member} />
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
}

function Header() {
  return (
    <thead>
      <tr>
        <HeaderCell className="pl-4 pr-3 sm:pl-0">Name</HeaderCell>
        <HeaderCell>Email</HeaderCell>
        <HeaderCell>Role</HeaderCell>
      </tr>
    </thead>
  );
}

function HeaderCell({
  children,
  className,
}: Readonly<{
  children: React.ReactNode;
  className?: string;
}>) {
  return (
    <th
      scope="col"
      className={cn(
        "text-foreground px-3 py-3.5 text-left text-sm font-semibold",
        className,
      )}
    >
      {children}
    </th>
  );
}

function MemberRow({
  member,
}: Readonly<{
  member: MemberProfile;
}>) {
  return (
    <tr>
      <MemberCell className="text-foreground pl-4 pr-3 font-medium sm:pl-0">
        {member.firstName} {member.lastName}
      </MemberCell>
      <MemberCell>{member.email}</MemberCell>
      <MemberCell>{member.isAdmin ? "Admin" : "Member"}</MemberCell>
    </tr>
  );
}

function MemberCell({
  children,
  className,
}: Readonly<{
  children: React.ReactNode;
  className?: string;
}>) {
  return (
    <td
      className={cn(
        "text-muted-foreground whitespace-nowrap px-3 py-4 text-sm",
        className,
      )}
    >
      {children}
    </td>
  );
}
