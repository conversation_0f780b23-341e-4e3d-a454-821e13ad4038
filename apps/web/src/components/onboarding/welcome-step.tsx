import Image from "next/image";
import { useRouter } from "next/navigation";

import { Button } from "@/web/components/ui/button";

export function WelcomeStep() {
  const router = useRouter();

  return (
    <div className="mx-auto flex h-[400px] max-w-md flex-col items-center justify-between">
      <div className="w-full text-center">
        <div className="mb-4 flex justify-center">
          <div className="flex h-[90px] w-[90px] items-center justify-center">
            <Image
              src="/pearl-logo.svg"
              alt="Pearl Logo"
              width={56}
              height={56}
              className="max-h-full max-w-full object-contain"
            />
          </div>
        </div>
        <h1 className="mb-8 text-2xl font-bold">Welcome to Pearl</h1>
      </div>

      <div className="mb-auto w-full max-w-md flex-grow overflow-auto text-center">
        <p className="mb-4 text-gray-600">
          <PERSON> is your AI partner for navigating complex sales. It helps you
          think through your deals, keep track of what matters, and move
          faster—without losing control.
        </p>
        <p className="mb-8 text-sm text-gray-500">
          This is an early beta, so things might shift quickly. We're learning
          with you. Thanks for being part of the journey.
        </p>
      </div>

      <div className="h-[60px] w-full max-w-md">
        <Button
          onClick={() => router.push("/salesforce")}
          className="w-full"
          size="lg"
        >
          Get started
        </Button>
      </div>
    </div>
  );
}
