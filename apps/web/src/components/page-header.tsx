import React from "react";

export type PageHeaderEnrichedItem = {
  icon: React.ReactNode;
  label: React.ReactNode;
};

interface PageHeaderProps {
  title: string;
  description?: string;
  action?: React.ReactNode;
  className?: string;
  variant?: "default" | "enriched" | "minimal";
  logoUrl?: string;
  descriptionItems?: PageHeaderEnrichedItem[];
}

export function PageHeader({
  title,
  description,
  action,
  className,
  variant = "default",
  logoUrl,
  descriptionItems = [],
}: PageHeaderProps) {
  if (variant === "minimal") {
    return (
      <div className={className ?? ""}>
        <h1
          className="text-2xl font-semibold"
          style={{ color: "var(--color-foreground)" }}
        >
          {title}
        </h1>
      </div>
    );
  }

  if (variant === "enriched") {
    return (
      <div className={`sm:flex sm:items-center ${className ?? ""}`}>
        <div className="w-full sm:flex-auto">
          {/* Mobile: logo centered above content */}
          {logoUrl && (
            <div className="mb-2 flex justify-center sm:hidden">
              <img
                src={logoUrl}
                alt="Logo"
                className="rounded object-cover"
                style={{ width: 62, height: 62, marginRight: 0 }}
              />
            </div>
          )}
          <div className="flex flex-col items-center gap-3 text-center sm:flex-row sm:text-left">
            {/* Desktop: logo left of content */}
            {logoUrl && (
              <img
                src={logoUrl}
                alt="Logo"
                className="hidden rounded object-cover sm:block"
                style={{ width: 62, height: 62, marginRight: 0 }}
              />
            )}
            <div className="flex flex-col gap-2">
              <h1
                className="text-2xl font-semibold"
                style={{ color: "var(--color-foreground)" }}
              >
                {title}
              </h1>
              {descriptionItems.length > 0 && (
                <ul
                  className="flex flex-row justify-center gap-2 overflow-x-auto pl-0 sm:justify-start"
                  style={{ paddingLeft: 0 }}
                >
                  {descriptionItems.map((item, idx) => (
                    <li
                      key={idx}
                      className="text-muted-foreground flex items-center gap-2 text-sm"
                    >
                      <span className="flex items-center justify-center">
                        {item.icon}
                      </span>
                      <span>{item.label}</span>
                    </li>
                  ))}
                </ul>
              )}
            </div>
          </div>
        </div>
        {action && (
          <div className="mt-4 flex w-full justify-center sm:ml-16 sm:mt-0 sm:w-auto sm:flex-none sm:justify-start">
            {action}
          </div>
        )}
      </div>
    );
  }

  // default
  return (
    <div className={`sm:flex sm:items-center ${className ?? ""}`}>
      <div className="sm:flex-auto">
        <h1
          className="text-2xl font-semibold"
          style={{ color: "var(--color-foreground)" }}
        >
          {title}
        </h1>
        {description && (
          <p
            className="mt-2 text-sm"
            style={{ color: "var(--color-muted-foreground)" }}
          >
            {description}
          </p>
        )}
      </div>
      {action && (
        <div className="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">{action}</div>
      )}
    </div>
  );
}

export default PageHeader;
