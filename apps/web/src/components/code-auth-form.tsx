"use client";

import { FormEvent, useState } from "react";

import { Button } from "@/web/components/ui/button";

export default function CodeAuthForm({
  token,
  onSubmit,
}: {
  token: string;
  onSubmit: (token: string, code: string) => void;
}) {
  const [code, setCode] = useState<string>("");
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState("");
  const [error, setError] = useState("");

  const handleChange = (e: FormEvent) => {
    const { value } = e.target as HTMLInputElement;
    setCode(value);
  };

  const handleSubmit = async (e: FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setMessage("");
    setError("");
    try {
      await onSubmit(token, code);
    } catch (err) {
      console.error("Error during code submission:", err);
      setError("An error occurred.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div>
      <h1>Input code</h1>
      <form onSubmit={handleSubmit}>
        <div>
          <label htmlFor="code">Code</label>
          <input
            type="string"
            id="code"
            name="code"
            value={code}
            onChange={handleChange}
            required
          />
        </div>

        <Button type="submit" disabled={isLoading}>
          {isLoading ? "Loading..." : "Submit"}
        </Button>
      </form>

      {message && <p>{message}</p>}
      {error && <p>{error}</p>}
    </div>
  );
}
